import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { DictEnum } from '@vben/constants';

import { getDataOptions, getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { getDeptTree } from '#/api/asset/assetProfile';
import { categoryTree } from '#/api/asset/category/type';

// 获取字典数据
const brandOptions = getDataOptions('brand');
const supplierOptions = getDataOptions('supplier');
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'keyword',
    label: '关键字',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.LEDGER_STATUS),
    },
    fieldName: 'brand',
    label: '台账性质',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.USE_STATUS),
    },
    fieldName: 'useStatus',
    label: '使用状态',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择使用科室',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'deptSection',
    label: '使用科室',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择管理部门',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'manageDept',
    label: '管理部门',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SPECIALTY_CATEGORY),
    },
    fieldName: 'specialtyCategory',
    label: '专业分类',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_TYPE),
    },
    fieldName: 'assetType',
    label: '资产类型',
  },

  {
    component: 'RangePicker',
    componentProps: {},
    fieldName: 'acceptanceDate',
    label: '验收日期',
  },
  {
    component: 'RangePicker',
    componentProps: {},
    fieldName: 'activationDate',
    label: '启用日期',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: () => categoryTree('ast_state_asset_category'),
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择国资分类',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'assetsClassification',
    label: '国资分类',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_CATEGORY),
    },
    fieldName: 'assetCategory',
    label: '资产类别',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TAG_TYPE),
    },
    fieldName: 'tagType',
    label: '物联标签类型',
  },
  {
    component: 'Select',
    fieldName: 'measurementMark',
    componentProps: {
      options: getDictOptions(DictEnum.MEASUREMENT_MARK),
    },
    label: '计量标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'qualityControlMark',
    label: '质控标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'pmMark',
    label: 'PM标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.PM_TYPE),
    },
    fieldName: 'pmType',
    label: 'PM分类',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择记账日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'bookkeepingDate',
    label: '记账日期',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入记账凭证号',
    },
    fieldName: 'accountingNum',
    label: '记账凭证号',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '标签二维码',
    field: 'qrCode',
  },
  {
    title: '资产编码',
    field: 'assetCode',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '通用名',
    field: 'commonName',
  },
  {
    title: '品牌',
    field: 'brand',
  },
  {
    title: '型号',
    field: 'model',
  },
  {
    title: '序列号',
    field: 'serialNumber',
  },
  {
    title: '生产厂家',
    field: 'manufacturer',
  },
  {
    title: '供应商',
    field: 'supplier',
  },
  {
    title: '使用状态',
    field: 'useStatus',
    slots: {
      default: ({ row }) => {
        return renderDict(row.useStatus, DictEnum.USE_STATUS);
      },
    },
  },
  {
    title: '资产类型',
    field: 'assetType',
    slots: {
      default: ({ row }) => {
        return renderDict(row.assetType, DictEnum.ASSETS_TYPE);
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '资产ID',
    fieldName: 'assetId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '资产编码',
    fieldName: 'assetCode',
    component: 'Input',
  },
  {
    label: '资产名称',
    fieldName: 'assetName',
    component: 'Input',
  },
  {
    label: '通用名',
    fieldName: 'commonName',
    component: 'Input',
  },
  {
    label: '资产类型',
    fieldName: 'assetType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_TYPE),
    },
  },
  {
    label: '品牌',
    fieldName: 'brand',
    component: 'Select',
    componentProps: {
      options: brandOptions,
    },
  },
  {
    label: '型号',
    fieldName: 'model',
    component: 'Input',
  },
  {
    label: '使用年限',
    fieldName: 'validityPeriod',
    component: 'Input',
  },
  {
    label: '生产厂家',
    fieldName: 'manufacturer',
    component: 'Input',
  },
  {
    label: '供应商',
    fieldName: 'supplier',
    component: 'Select',
    componentProps: {
      options: supplierOptions,
    },
  },
  {
    label: '产地',
    fieldName: 'originCountry',
    component: 'Input',
  },
  {
    label: '医疗器械注册/备案号',
    fieldName: 'registrationNumber',
    component: 'Input',
  },
  {
    label: '计量单位',
    fieldName: 'measurementUnit',
    component: 'Input',
  },
  {
    label: '固定资产分类代码',
    fieldName: 'fixedAssetCode',
    component: 'Input',
  },
  {
    label: '专业分类',
    fieldName: 'specialtyCategory',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SPECIALTY_CATEGORY 便于维护
      options: getDictOptions(DictEnum.SPECIALTY_CATEGORY),
    },
  },
  {
    label: '医疗器械分类',
    fieldName: 'medicalDeviceCategory',
    component: 'Input',
  },
  {
    label: '资产类别',
    fieldName: 'assetCategory',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.ASSETS_CATEGORY 便于维护
      options: getDictOptions(DictEnum.ASSETS_CATEGORY),
    },
  },
];
