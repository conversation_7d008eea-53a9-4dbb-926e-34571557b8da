<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Button, Card, Col, message, Row, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenModal } from '@vben/common-ui';
import { assetTransferAdd, assetTransferInfo, assetTransferUpdate } from '#/api/asset/assetTransfer';
import type { AssetProfileVO } from '#/api/asset/assetProfile/model';

import AssetDetailModalComponent from './asset-detail-modal.vue';
import { assetSelectFormOptions, assetSelectGridOptions, createAssetSelectGridEvents, modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const route = useRoute();
const router = useRouter();

// 获取资产调拨ID
const transferId = ref(route.query.id as string);
const isLoading = ref(false);
const isUpdate = computed(() => !!transferId.value);

// 选中的资产
const selectedAsset = ref<AssetProfileVO | null>(null);

const title = computed(() => {
  return isUpdate.value ? '编辑资产调拨' : '新增资产调拨';
});

const [AssetTransferForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    }
  },
  schema: modalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
});

// 处理双击查看资产详情
function handleAssetRowDblClick(params: any) {
  console.log('handleAssetRowDblClick', params);
  const { row } = params;
  assetDetailModalApi.setData({ assetId: row.assetId });
  assetDetailModalApi.open();
}

const [AssetSelectTable, assetTableApi] = useVbenVxeGrid({
  formOptions: assetSelectFormOptions,
  gridOptions: assetSelectGridOptions,
  gridEvents: createAssetSelectGridEvents(handleAssetRowDblClick),
});

// 资产详情模态框
const [AssetDetailModal, assetDetailModalApi] = useVbenModal({
  connectedComponent: AssetDetailModalComponent,
});

// 加载数据
async function loadData() {
  if (!isUpdate.value || !transferId.value) {
    return;
  }

  try {
    isLoading.value = true;
    const record = await assetTransferInfo(transferId.value);
    await formApi.setValues(record);
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    isLoading.value = false;
  }
}

// 保存数据
async function handleSave() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }
    
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());
    
    if (isUpdate.value) {
      await assetTransferUpdate(data);
      message.success('更新成功');
    } else {
      await assetTransferAdd(data);
      message.success('新增成功');
    }
    
    emit('reload');
    handleBack();
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  } finally {
    isLoading.value = false;
  }
}

// 返回列表
function handleBack() {
  router.push('/asset/assetTransfer');
}

// 处理资产选择
function handleAssetSelect({ row }: { row: AssetProfileVO }) {
  selectedAsset.value = row;
  // 自动填充表单字段
  formApi.setValues({
    assetCode: row.assetCode,
    assetName: row.assetName,
    brand: row.brand,
    model: row.model,
    serialNumber: row.assetCode, 
  });
}

// 查询资产列表
function handleAssetQuery() {
  assetTableApi.query();
}

// 重置资产查询
function handleAssetReset() {
  assetTableApi.formApi.resetForm();
  assetTableApi.query();
}



// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <Page>
    <Row :gutter="[15, 15]">
      <Col :span="24">
        <Card>
          <template #title>
            <span style="font-size: 20px">{{ title }}</span>
          </template>
          <template #extra>
            <Space>
              <Button @click="handleBack">
                返回
              </Button>
              <Button type="primary" :loading="isLoading" @click="handleSave">
                保存
              </Button>
            </Space>
          </template>
          <AssetTransferForm />
        </Card>
      </Col>

      <!-- 资产选择列表 -->
      <Col :span="24">
        <Card title="选择资产">
          <template #extra>
            <span style="color: #666; font-size: 12px;">提示：双击行可查看资产详情</span>
          </template>
          <AssetSelectTable @radio-change="handleAssetSelect">
            <template #toolbar-actions>
              <Space>
                <Button type="primary" @click="handleAssetQuery">查询</Button>
                <Button @click="handleAssetReset">重置</Button>
              </Space>
            </template>
          </AssetSelectTable>
        </Card>
      </Col>
    </Row>

    <!-- 资产详情模态框 -->
    <AssetDetailModal />
  </Page>
</template>
