import type { AssetTransferVO, AssetTransferForm, AssetTransferQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询资产转科列表
* @param params
* @returns 资产转科列表
*/
export function assetTransferList(params?: AssetTransferQuery) {
  return requestClient.get<PageResult<AssetTransferVO>>('/asset/assetTransfer/list', { params });
}

/**
 * 导出资产转科列表
 * @param params
 * @returns 资产转科列表
 */
export function assetTransferExport(params?: AssetTransferQuery) {
  return commonExport('/asset/assetTransfer/export', params ?? {});
}

/**
 * 查询资产转科详情
 * @param transferId id
 * @returns 资产转科详情
 */
export function assetTransferInfo(transferId: ID) {
  return requestClient.get<AssetTransferVO>(`/asset/assetTransfer/${transferId}`);
}

/**
 * 新增资产转科
 * @param data
 * @returns void
 */
export function assetTransferAdd(data: AssetTransferForm) {
  return requestClient.postWithMsg<void>('/asset/assetTransfer', data);
}

/**
 * 更新资产转科
 * @param data
 * @returns void
 */
export function assetTransferUpdate(data: AssetTransferForm) {
  return requestClient.putWithMsg<void>('/asset/assetTransfer', data);
}

/**
 * 删除资产转科
 * @param transferId id
 * @returns void
 */
export function assetTransferRemove(transferId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/asset/assetTransfer/${transferId}`);
}
