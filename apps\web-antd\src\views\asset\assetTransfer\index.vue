<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';
import { cancelProcessApply } from '#/api/workflow/instance';

import {
  assetTransferExport,
  assetTransferList,
  assetTransferRemove,
} from '#/api/asset/assetTransfer';
import type { AssetTransferForm } from '#/api/asset/assetTransfer/model';
import { commonDownloadExcel } from '#/utils/file/download';

import { flowInfoModal } from '#/views/workflow/components';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
    checkMethod: ({ row }: any) => {
      // 只有草稿、驳回、撤销状态的记录可以选中删除
      return ['draft', 'back', 'cancel'].includes(row.flowStatus);
    },
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await assetTransferList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'transferId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'asset-assetTransfer-index'
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [FlowInfoModal, flowInfoModalApi] = useVbenModal({
  connectedComponent: flowInfoModal,
});

const router = useRouter();

function handleAdd() {
  router.push('/asset/assetTransfer/add');
}

async function handleEdit(row: Required<AssetTransferForm>) {
  router.push({
    path: '/asset/assetTransfer/edit',
    query: {
      id: row.transferId,
    },
  });
}

async function handleDelete(row: Required<AssetTransferForm>) {
  await assetTransferRemove(row.transferId);
  await tableApi.query();
}

async function handleRevoke(row: Required<AssetTransferForm>) {
  await cancelProcessApply({
    businessId: row.transferId,
    message: '申请人撤销流程！',
  });
  await tableApi.query();
}

function handleFlowInfo(row: Required<AssetTransferForm>) {
  flowInfoModalApi.setData({ businessId: row.transferId });
  flowInfoModalApi.open();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<AssetTransferForm>) => row.transferId);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await assetTransferRemove(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(assetTransferExport, '资产转科数据', tableApi.formApi.form.values, {
    fieldMappingTime: formOptions.fieldMappingTime,
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="资产转科列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['asset:assetTransfer:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary" 
            v-access:code="['asset:assetTransfer:remove']" 
            @click="handleMultiDelete">
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['asset:assetTransfer:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <!-- 草稿、驳回、撤销状态可以编辑 -->
          <ghost-button
            v-if="['draft', 'back', 'cancel'].includes(row.flowStatus)"
            v-access:code="['asset:assetTransfer:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>

          <!-- 进行中状态可以撤销 -->
          <Popconfirm
            v-if="row.flowStatus === 'waiting'"
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确定要撤销该申请吗？"
            @confirm="handleRevoke(row)"
          >
            <ghost-button
              danger
              @click.stop=""
            >
              撤销
            </ghost-button>
          </Popconfirm>

          <!-- 草稿、驳回、撤销状态可以删除 -->
          <Popconfirm
            v-if="['draft', 'back', 'cancel'].includes(row.flowStatus)"
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['asset:assetTransfer:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>

          <!-- 查看流程详情 -->
          <ghost-button @click.stop="handleFlowInfo(row)">
            详情
          </ghost-button>
        </Space>
      </template>
    </BasicTable>
    <FlowInfoModal />
  </Page>
</template>
