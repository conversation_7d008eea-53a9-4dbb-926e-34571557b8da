<script setup lang="ts">
import type { User } from '#/api/system/user/model';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Input } from 'ant-design-vue';

import { userInfo } from '#/api/system/user';
import { userSelectModal } from '#/views/workflow/components';

defineOptions({
  name: 'UserSelect',
  inheritAttrs: false,
});

interface Props {
  value?: string; // userId
  placeholder?: string;
  disabled?: boolean;
}

interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', value: string): void;
  (e: 'input', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择申请人',
  disabled: false,
});

const emit = defineEmits<Emits>();

// 显示的用户名
const displayName = ref('');
// 存储的用户ID
const userId = ref(props.value || '');

// 用户选择模态框
const [UserSelectModal, modalApi] = useVbenModal({
  connectedComponent: userSelectModal,
});

// 根据userId获取用户信息
async function fetchUserInfo(userId: string) {
  try {
    const user = await userInfo(userId);
    displayName.value = user.nickName || user.userName || userId;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    displayName.value = userId; // 失败时显示ID
  }
}

// 监听外部值变化
watch(
  () => props.value,
  async (newValue) => {
    userId.value = newValue || '';
    // 如果有值但没有显示名称，需要根据ID获取用户信息
    if (newValue && !displayName.value) {
      await fetchUserInfo(newValue);
    } else if (!newValue) {
      displayName.value = '';
    }
  },
  { immediate: true }
);

// 打开用户选择弹窗
function handleOpenUserSelect() {
  if (props.disabled) return;
  
  modalApi.setData({
    userList: [], // 当前选中的用户列表
  });
  modalApi.open();
}

// 处理用户选择完成
function handleUserSelectFinish(userList: User[]) {
  if (userList.length > 0) {
    const selectedUser = userList[0]; // 取第一个用户（单选）
    userId.value = selectedUser.userId;
    displayName.value = selectedUser.nickName;
    
    // 发出事件
    emit('update:value', selectedUser.userId);
    emit('change', selectedUser.userId);
    emit('input', selectedUser.userId);
  }
}

// 清除选择
function handleClear() {
  if (props.disabled) return;
  
  userId.value = '';
  displayName.value = '';
  emit('update:value', '');
  emit('change', '');
  emit('input', '');
}
</script>

<template>
  <div class="user-select">
    <Input
      :value="displayName"
      :placeholder="placeholder"
      :disabled="disabled"
      readonly
      @click="handleOpenUserSelect"
    >
      <template #suffix>
        <div class="flex items-center gap-1">
          <span
            v-if="displayName && !disabled"
            class="cursor-pointer text-gray-400 hover:text-gray-600"
            @click.stop="handleClear"
          >
            ✕
          </span>
          <span
            v-if="!disabled"
            class="cursor-pointer text-gray-400 hover:text-gray-600"
            @click.stop="handleOpenUserSelect"
          >
            👤
          </span>
        </div>
      </template>
    </Input>
    
    <!-- 用户选择模态框 -->
    <UserSelectModal
      mode="single"
      @finish="handleUserSelectFinish"
    />
  </div>
</template>

<style scoped>
.user-select :deep(.ant-input) {
  cursor: pointer;
}

.user-select :deep(.ant-input[disabled]) {
  cursor: not-allowed;
}
</style>
