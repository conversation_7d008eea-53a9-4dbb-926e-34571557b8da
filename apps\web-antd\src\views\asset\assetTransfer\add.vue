<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { Button, Card, Col, message, Row, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { useVbenModal } from '@vben/common-ui';
import { assetTransferAdd } from '#/api/asset/assetTransfer';
import type { AssetProfileVO } from '#/api/asset/assetProfile/model';

import AssetDetailModalComponent from './asset-detail-modal.vue';
import { assetSelectFormOptions, assetSelectGridOptions, createAssetSelectGridEvents, modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const router = useRouter();
const isLoading = ref(false);

// 选中的资产
const selectedAsset = ref<AssetProfileVO | null>(null);

const [AssetTransferForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    }
  },
  schema: modalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
});

// 处理双击查看资产详情
function handleAssetRowDblClick(params: any) {
  console.log('handleAssetRowDblClick', params);
  const { row } = params;
  assetDetailModalApi.setData({ assetId: row.assetId });
  assetDetailModalApi.open();
}

const [AssetSelectTable, assetTableApi] = useVbenVxeGrid({
  formOptions: assetSelectFormOptions,
  gridOptions: assetSelectGridOptions,
  gridEvents: {
    ...createAssetSelectGridEvents(handleAssetRowDblClick),
    radioChange: handleAssetSelect, // 添加 radio 选择事件
  },
});

// 资产详情模态框
const [AssetDetailModal, assetDetailModalApi] = useVbenModal({
  connectedComponent: AssetDetailModalComponent,
});

// 保存数据
async function handleSave() {
  try {
    isLoading.value = true;
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    await assetTransferAdd(data);
    message.success('新增成功');

    emit('reload');
    handleBack();
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  } finally {
    isLoading.value = false;
  }
}

// 返回列表
function handleBack() {
  router.push('/asset/assetTransfer');
}

// 处理资产选择
function handleAssetSelect(params: any) {
  console.log('handleAssetSelect 事件被触发!', params);

  // VXE Table 的 radio-change 事件参数结构可能是 { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }
  let row: AssetProfileVO;

  if (params.row) {
    row = params.row as AssetProfileVO;
  } else if (params && typeof params === 'object' && params.assetId) {
    // 如果参数直接是行数据
    row = params as AssetProfileVO;
  } else {
    console.error('handleAssetSelect: 无法识别的参数结构', params);
    return;
  }
  selectedAsset.value = row;

  // 自动填充表单字段 - 只同步表单中存在的字段
  const formData: Record<string, any> = {};

  // 定义字段映射关系：表单字段名 -> 资产数据字段名
  const fieldMapping = {
    assetCode: 'assetCode',
    assetName: 'assetName',
    brand: 'brand',
    model: 'model',
    serialNumber: 'assetCode', // 暂时使用资产编码，如果有专门的序列号可以改为其他字段
  };

  // 遍历映射关系，同步有值的字段
  Object.entries(fieldMapping).forEach(([formField, assetField]) => {
    const value = row[assetField as keyof AssetProfileVO];
    if (value !== undefined && value !== null && value !== '') {
      formData[formField] = value;
    }
  });

  console.log('选中的资产数据:', row);
  console.log('同步到表单的数据:', formData);

  // 批量设置表单值
  formApi.setValues(formData);

  // 用户提示
  const syncedFields = Object.keys(formData);
  if (syncedFields.length > 0) {
    message.success(`已自动填充 ${syncedFields.length} 个字段：${syncedFields.join('、')}`);
  }
}

// 查询资产列表
function handleAssetQuery() {
  assetTableApi.query();

  // 调试：检查表格状态
  setTimeout(() => {
    console.log('表格实例:', assetTableApi.grid);
    console.log('表格数据行数:', assetTableApi.grid?.getData()?.length);
  }, 1000);
}

// 重置资产查询
function handleAssetReset() {
  assetTableApi.formApi.resetForm();
  assetTableApi.query();
}

// 清除资产选择
function handleClearAssetSelection() {
  selectedAsset.value = null;
  // 清除相关表单字段
  formApi.setValues({
    assetCode: '',
    assetName: '',
    brand: '',
    model: '',
    serialNumber: '',
  });
  message.info('已清除资产选择');
}

// 测试函数 - 用于调试
function handleTestAssetSelect() {
  console.log('测试资产选择功能');
  const testAsset: AssetProfileVO = {
    assetId: 'test-001',
    assetCode: 'TEST001',
    assetName: '测试资产',
    brand: '测试品牌',
    model: '测试型号',
    commonName: '测试通用名',
    assetType: '医疗设备',
    manufacturer: 1,
    supplier: 1,
    validityPeriod: 10,
    originCountry: '中国',
    measurementUnit: '台',
    fixedAssetCode: 'FA001',
    specialtyCategory: '医疗设备',
    medicalDeviceCategory: '诊断设备',
    assetCategory: '固定资产',
    registrationNumber: 'REG001',
  };

  handleAssetSelect({ row: testAsset });
}

// 组件挂载时的初始化
onMounted(() => {
  // 初始化表单数据
  formApi.setValues({
    applicationDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
  });

  // 调试：检查表格配置
  setTimeout(() => {
    console.log('=== 表格调试信息 ===');
    console.log('表格实例:', assetTableApi.grid);
    console.log('表格配置 radioConfig:', assetSelectGridOptions.radioConfig);
    console.log('表格配置 columns:', assetSelectGridOptions.columns);

    // 检查第一列是否是 radio 类型
    const firstColumn = assetSelectGridOptions.columns?.[0];
    console.log('第一列配置:', firstColumn);

    // 检查事件绑定
    console.log('gridEvents:', {
      ...createAssetSelectGridEvents(handleAssetRowDblClick),
      radioChange: handleAssetSelect,
    });
  }, 2000);
});

</script>

<template>
  <Page>
    <Row :gutter="[15, 15]">
      <Col :span="24">
      <Card>
        <template #title>
          <span style="font-size: 20px">新增资产调拨</span>
        </template>
        <template #extra>
          <Space>
            <Button @click="handleBack">
              返回
            </Button>
            <Button type="primary" :loading="isLoading" @click="handleSave">
              保存
            </Button>
          </Space>
        </template>
        <AssetTransferForm />
      </Card>
      </Col>

      <!-- 资产选择列表 -->
      <Col :span="24">
      <Card>
        <template #title>
          <span>选择资产</span>
          <span v-if="selectedAsset" style="color: #1890ff; margin-left: 10px;">
            （已选择：{{ selectedAsset.assetCode }} - {{ selectedAsset.assetName }}）
          </span>
        </template>
        <template #extra>
          <span style="color: #666; font-size: 12px;">提示：双击行可查看资产详情</span>
        </template>
        <AssetSelectTable>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="handleAssetQuery">查询</Button>
              <Button @click="handleAssetReset">重置</Button>
              <Button
                v-if="selectedAsset"
                type="default"
                danger
                @click="handleClearAssetSelection"
              >
                清除选择
              </Button>
              <Button
                type="dashed"
                @click="handleTestAssetSelect"
              >
                测试选择
              </Button>
            </Space>
          </template>
        </AssetSelectTable>
      </Card>
      </Col>
    </Row>

    <!-- 资产详情模态框 -->
    <AssetDetailModal />
  </Page>
</template>
