import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils/render';
import { getDeptTree } from '#/api/asset/assetProfile';
import { categoryTree } from '#/api/asset/category/type';
import { getDataOptions, getDictOptions } from '#/utils/dict';

// 获取字典数据
const brandOptions = getDataOptions('brand');
const supplierOptions = getDataOptions('supplier');

export const baseSchema: FormSchemaGetter = () => [
  {
    label: '资产ID',
    fieldName: 'assetId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产名称',
    },
    fieldName: 'assetName',
    label: '资产名称',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入通用名',
    },
    fieldName: 'commonName',
    label: '通用名',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_TYPE),
      placeholder: '请选择资产类型',
    },
    fieldName: 'assetType',
    label: '资产类型',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择固定资产分类',
    },
    fieldName: 'fixedAssetCode',
    label: '固定资产分类',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择品牌',
      options: brandOptions,
    },
    fieldName: 'brand',
    label: '品牌',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入型号',
    },
    fieldName: 'model',
    label: '型号',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请输入生产厂家',
      options: supplierOptions,
      showSearch: true,
      optionFilterProp: 'label',
      filterOption: (input: string, option: any) => {
        return option?.label?.includes(input);
      },
      allowClear: true,
    },
    fieldName: 'manufacturer',
    label: '生产厂家',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择供应商',
      options: supplierOptions,
      showSearch: true,
      optionFilterProp: 'label',
      filterOption: (input: string, option: any) => {
        return option?.label?.includes(input);
      },
      allowClear: true,
    },
    fieldName: 'supplier',
    label: '供应商',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入产地',
    },
    fieldName: 'originCountry',
    label: '产地',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入计量单位',
    },
    fieldName: 'measurementUnit',
    label: '计量单位',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入使用年限',
    },
    fieldName: 'validityPeriod',
    label: '使用年限',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入医疗器械注册/备案号',
    },
    fieldName: 'registrationNumber',
    label: '  医疗器械注册/备案号',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SPECIALTY_CATEGORY),
    },
    fieldName: 'specialtyCategory',
    label: '专业分类',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择医疗器械分类',
    },
    fieldName: 'medicalDeviceCategory',
    label: '医疗器械分类',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请选择医疗器械编号',
    },
    fieldName: 'medicalDeviceNumber',
    label: '医疗器械编号',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入备注',
    },
    fieldName: 'remark',
    label: '备注',
  },
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'status',
    label: '状态',
  }
];

export const financeSchema: FormSchemaGetter = () => [
  {
    label: '财务ID',
    fieldName: 'financeId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入原值',
    },
    fieldName: 'contractPrice',
    label: '原值',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择启用日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'activationDate',
    label: '启用日期',
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      placeholder: '请选择折旧方法',
      options: getDictOptions(DictEnum.DEPRECIATION_METHOD),
    },
    fieldName: 'depreciationMethod',
    label: '折旧方法',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入折旧年限',
    },
    fieldName: 'depreciationYears',
    label: '折旧年限',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入残值率',
    },
    fieldName: 'residualRate',
    label: '残值率',
    rules: 'required',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入财政拨款金额',
    },
    fieldName: 'financeAppropriation',
    label: '财政拨款',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入科研专项金额',
    },
    fieldName: 'researchProject',
    label: '科研专项',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入自筹资金金额',
    },
    fieldName: 'selfRaised',
    label: '自筹资金',
  },
];
export const entityListSchema: FormSchemaGetter = () => [
  {
    label: '管理ID',
    fieldName: 'managementId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入标签二维码',
    },
    fieldName: 'entityList[0].qrCode',
    label: '标签二维码',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入序列号',
    },
    fieldName: 'entityList[0].serialNumber',
    label: '序列号',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择使用科室',
    },
    fieldName: 'entityList[0].deptSection',
    label: '使用科室',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请输入安装位置',
    },
    fieldName: 'entityList[0].installationLocation',
    label: '安装位置',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请输入生产日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'entityList[0].productionDate',
    label: '生产日期',
    rules: 'required',
  },
  {
    component: 'RangePicker',
    componentProps: {
      placeholder: '请输入保修期限',
    },
    defaultValue: undefined,
    fieldName: 'entityList[0].warrantyDate',
    label: '保险期限',
    rules: 'selectRequired',
  },
  {
    label: '图片上传',
    component: 'ImageUpload',
    fieldName: 'entityList[0].itemPhotoList',
    componentProps: {
      maxCount: 5,
    },
  },
  {
    component: 'Br',
    fieldName: '',
    label: '',
  },
];

export const entitySchema: FormSchemaGetter = () => [
  {
    label: '管理ID',
    fieldName: 'managementId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入标签二维码',
    },
    fieldName: 'entityList[0].qrCode',
    label: '标签二维码',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入序列号',
    },
    fieldName: 'entityList[0].serialNumber',
    label: '序列号',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择使用科室',
    },
    fieldName: 'entityList[0].deptSection',
    label: '使用科室',
    rules: 'required',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请输入安装位置',
    },
    fieldName: 'entityList[0].installationLocation',
    label: '安装位置',
    rules: 'required',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请输入生产日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'entityList[0].productionDate',
    label: '生产日期',
    rules: 'required',
  },
  {
    component: 'RangePicker',
    componentProps: {
      placeholder: '请输入保修期限',
    },
    defaultValue: undefined,
    fieldName: 'entityList[0].warrantyDate',
    label: '保险期限',
    rules: 'selectRequired',
  },
  {
    label: '图片上传',
    component: 'ImageUpload',
    fieldName: 'entityList[0].itemPhotoList',
    componentProps: {
      maxCount: 5,
    },
  },
  {
    component: 'Br',
    fieldName: '',
    label: '',
  },
];

export const entityEditSchema: FormSchemaGetter = () => [
  {
    label: '管理ID',
    fieldName: 'managementId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    fieldName: 'ledgerStatus',
    defaultValue: '账实相符',
    label: '台账性质',
  },
  {
    component: 'Select',
    componentProps: {
      disabled: true,
      options: getDictOptions(DictEnum.USE_STATUS),
    },
    fieldName: 'useStatus',
    label: '使用状态',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择使用科室',
      disabled: true,
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      treeNodeLabelProp: 'fullName',
    },
    fieldName: 'deptSection',
    label: '使用科室',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入标签二维码',
    },
    fieldName: 'qrCode',
    label: '标签二维码',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择管理科室',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      treeNodeLabelProp: 'fullName',
    },
    fieldName: 'manageDept',
    label: '管理科室',
  },

  {
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请输入安装位置',
    },
    fieldName: 'installationLocation',
    label: '安装位置',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    fieldName: 'location',
    label: '地理位置',
  },
  {
    component: 'Input',
    componentProps: {},
    fieldName: 'assetManager',
    label: '资产管理员',
  },
  {
    label: '图片上传',
    component: 'ImageUpload',
    fieldName: 'itemPhotoList',
    componentProps: {
      maxCount: 5,
    },
  },
];

export const otherInfoSchema: FormSchemaGetter = () => [
  {
    label: '资产ID',
    fieldName: 'assetId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TAG_TYPE),
    },
    fieldName: 'tagType',
    label: '物联标签类型',
  },
  {
    component: 'Select',
    fieldName: 'measurementMark',
    componentProps: {
      options: getDictOptions(DictEnum.MEASUREMENT_MARK),
    },
    label: '计量标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'qualityControlMark',
    label: '质控标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'pmMark',
    label: 'PM标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.PM_TYPE),
    },
    fieldName: 'pmCategory',
    label: 'PM分类',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择记账日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'bookkeepingDate',
    label: '记账日期',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入记账凭证号',
    },
    fieldName: 'accountingVoucherNumber',
    label: '记账凭证号',
  },
];

export const historySchema: FormSchemaGetter = () => [
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择导入时间',
      format: 'YYYY-MM-DD',
      disabled: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    defaultValue: '2022-01-01 00:00:00',
    fieldName: 'createTime',
    label: '导入时间',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    defaultValue: 'OLD_23109247',
    fieldName: 'assetCode',
    label: '资产编码',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    defaultValue: '多参数监护仪（旧）',
    fieldName: 'assetName',
    label: '资产名称',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    defaultValue: 'BeneBiew T5(旧版)',
    fieldName: 'model',
    label: '规格型号',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    defaultValue: 'MR2023891',
    fieldName: 'serialNumber',
    label: '序列号',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    defaultValue: '急诊科',
    fieldName: 'deptSection',
    label: '使用科室',
  },
];

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'keyword',
    label: '关键字',
  },
  {
    component: 'Input',
    fieldName: 'status',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    defaultValue: '0',
    label: '状态',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.LEDGER_STATUS),
    },
    fieldName: 'brand',
    label: '台账性质',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.USE_STATUS),
    },
    fieldName: 'useStatus',
    label: '使用状态',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择使用科室',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'deptSection',
    label: '使用科室',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择管理部门',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'manageDept',
    label: '管理部门',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SPECIALTY_CATEGORY),
    },
    fieldName: 'specialtyCategory',
    label: '专业分类',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_TYPE),
    },
    fieldName: 'assetType',
    label: '资产类型',
  },

  {
    component: 'RangePicker',
    componentProps: {},
    fieldName: 'acceptanceDate',
    label: '验收日期',
  },
  {
    component: 'RangePicker',
    componentProps: {},
    fieldName: 'activationDate',
    label: '启用日期',
  },
  {
    component: 'ApiTreeSelect',
    componentProps: {
      api: () => categoryTree('ast_state_asset_category'),
      allowClear: true,
      class: 'w-full',
      fieldNames: {
        key: 'id',
        value: 'id',
        children: 'children',
        label: 'label',
      },
      placeholder: '请选择国资分类',
      showSearch: true,
      treeDefaultExpandAll: true,
      treeLine: { showLeafIcon: false },
      treeNodeFilterProp: 'label',
    },
    fieldName: 'assetsClassification',
    label: '国资分类',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.ASSETS_CATEGORY),
    },
    fieldName: 'assetCategory',
    label: '资产类别',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TAG_TYPE),
    },
    fieldName: 'tagType',
    label: '物联标签类型',
  },
  {
    component: 'Select',
    fieldName: 'measurementMark',
    componentProps: {
      options: getDictOptions(DictEnum.MEASUREMENT_MARK),
    },
    label: '计量标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'qualityControlMark',
    label: '质控标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_YES_NO),
    },
    fieldName: 'pmMark',
    label: 'PM标志',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.PM_TYPE),
    },
    fieldName: 'pmType',
    label: 'PM分类',
  },
  {
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择记账日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'bookkeepingDate',
    label: '记账日期',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入记账凭证号',
    },
    fieldName: 'accountingNum',
    label: '记账凭证号',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '标签二维码',
    field: 'qrCode',
  },
  {
    title: '资产编码',
    field: 'assetCode',
  },
  {
    title: '资产名称',
    field: 'assetName',
  },
  {
    title: '通用名',
    field: 'commonName',
  },
  {
    title: '品牌',
    field: 'brand',
  },
  {
    title: '型号',
    field: 'model',
  },
  {
    title: '序列号',
    field: 'serialNumber',
  },
  {
    title: '生产厂家',
    field: 'manufacturer',
  },
  {
    title: '供应商',
    field: 'supplier',
  },
  {
    title: '使用状态',
    field: 'useStatus',
  },
  {
    title: '资产类型',
    field: 'assetType',
    slots: {
      default: ({ row }) => {
        return renderDict(row.assetType, DictEnum.ASSETS_TYPE);
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];
