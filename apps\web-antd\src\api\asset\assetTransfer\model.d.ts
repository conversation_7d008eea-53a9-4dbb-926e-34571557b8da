import type { PageQuery, BaseEntity } from '#/api/common';

export interface AssetTransferVO {
  /**
   * 转科单号
   */
  transferId: string | number;

  /**
   * 资产编码
   */
  assetCode: string;

  /**
   * 转出科室
   */
  deptOutId: string | number;

  /**
   * 转出科室
   */
  deptInId: string | number;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 	出厂编码
   */
  serialNumber: string;

  /**
   * 管理部门
   */
  manageDept: number;

  /**
   * 申请人
   */
  applicant: number;

  /**
   * 申请日期
   */
  applicationDate: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 工作流状态
   */
  flowStatus?: string;

}

export interface AssetTransferForm extends BaseEntity {
  /**
   * 转科单号
   */
  transferId?: string | number;

  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 转出科室
   */
  deptOutId?: string | number;

  /**
   * 转出科室
   */
  deptInId?: string | number;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 	出厂编码
   */
  serialNumber?: string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 申请人
   */
  applicant?: number;

  /**
   * 申请日期
   */
  applicationDate?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 工作流状态
   */
  flowStatus?: string;

}

export interface AssetTransferQuery extends PageQuery {
  /**
   * 资产编码
   */
  assetCode?: string;

  /**
   * 转出科室
   */
  deptOutId?: string | number;

  /**
   * 转出科室
   */
  deptInId?: string | number;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 	出厂编码
   */
  serialNumber?: string;

  /**
   * 管理部门
   */
  manageDept?: number;

  /**
   * 申请人
   */
  applicant?: number;

  /**
   * 申请日期
   */
  applicationDate?: string;

  /**
   * 状态
   */
  status?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
